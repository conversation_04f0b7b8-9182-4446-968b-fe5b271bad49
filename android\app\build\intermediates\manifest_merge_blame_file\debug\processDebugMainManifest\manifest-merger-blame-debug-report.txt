1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ramsha_malik.tms_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:4:3-75
11-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:5:3-63
16-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:5:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:6:3-78
17-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:6:20-76
18
19    <queries>
19-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:7:3-13:13
20        <intent>
20-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:8:5-12:14
21            <action android:name="android.intent.action.VIEW" />
21-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:7-58
21-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:7-67
23-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:17-65
24
25            <data android:scheme="https" />
25-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:7-37
25-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:13-35
26        </intent>
27        <!-- Query open documents -->
28        <intent>
28-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
29-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
30        </intent>
31    </queries>
32
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
33-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
34    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
34-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
34-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
35-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
36-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-92
36-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-89
37
38    <permission
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.ramsha_malik.tms_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.ramsha_malik.tms_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43
44    <application
44-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:3-32:17
45        android:name="com.ramsha_malik.tms_app.MainApplication"
45-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:16-47
46        android:allowBackup="true"
46-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:162-188
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:icon="@mipmap/ic_launcher"
50-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:81-115
51        android:label="@string/app_name"
51-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:48-80
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:116-161
53        android:supportsRtl="true"
53-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:221-247
54        android:theme="@style/AppTheme"
54-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:189-220
55        android:usesCleartextTraffic="true" >
55-->C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:6:18-53
56        <meta-data
56-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:15:5-82
57            android:name="expo.modules.updates.ENABLED"
57-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:15:16-59
58            android:value="true" />
58-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:15:60-80
59        <meta-data
59-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:16:5-119
60            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
60-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:16:16-72
61            android:value="@string/expo_runtime_version" />
61-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:16:73-117
62        <meta-data
62-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:17:5-105
63            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
63-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:17:16-80
64            android:value="ALWAYS" />
64-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:17:81-103
65        <meta-data
65-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:18:5-99
66            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
66-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:18:16-79
67            android:value="0" />
67-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:18:80-97
68        <meta-data
68-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:19:5-141
69            android:name="expo.modules.updates.EXPO_UPDATE_URL"
69-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:19:16-67
70            android:value="https://u.expo.dev/aec7ddf4-9474-4131-9a9a-4b9309bf4176" />
70-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:19:68-139
71
72        <activity
72-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:5-31:16
73            android:name="com.ramsha_malik.tms_app.MainActivity"
73-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:15-43
74            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
74-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:44-134
75            android:exported="true"
75-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:256-279
76            android:launchMode="singleTask"
76-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:135-166
77            android:screenOrientation="portrait"
77-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:280-316
78            android:theme="@style/Theme.App.SplashScreen"
78-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:210-255
79            android:windowSoftInputMode="adjustResize" >
79-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:167-209
80            <intent-filter>
80-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:21:7-24:23
81                <action android:name="android.intent.action.MAIN" />
81-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:22:9-60
81-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:22:17-58
82
83                <category android:name="android.intent.category.LAUNCHER" />
83-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:23:9-68
83-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:23:19-66
84            </intent-filter>
85            <intent-filter>
85-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:25:7-30:23
86                <action android:name="android.intent.action.VIEW" />
86-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:7-58
86-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:15-56
87
88                <category android:name="android.intent.category.DEFAULT" />
88-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:27:9-67
88-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:27:19-65
89                <category android:name="android.intent.category.BROWSABLE" />
89-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:7-67
89-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:17-65
90
91                <data android:scheme="tmsapp" />
91-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:7-37
91-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:13-35
92            </intent-filter>
93        </activity>
94
95        <provider
95-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
96            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
96-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
97            android:authorities="com.ramsha_malik.tms_app.fileprovider"
97-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
98            android:exported="false"
98-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
99            android:grantUriPermissions="true" >
99-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
100            <meta-data
100-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
101                android:name="android.support.FILE_PROVIDER_PATHS"
101-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
102                android:resource="@xml/file_provider_paths" />
102-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
103        </provider>
104
105        <meta-data
105-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
106            android:name="org.unimodules.core.AppLoader#react-native-headless"
106-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
107            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
107-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
108        <meta-data
108-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
109            android:name="com.facebook.soloader.enabled"
109-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
110            android:value="true" />
110-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
111
112        <activity
112-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:49
113            android:name="expo.modules.video.FullscreenPlayerActivity"
113-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-71
114            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
114-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-91
115            android:supportsPictureInPicture="true"
115-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-52
116            android:theme="@style/Fullscreen" />
116-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-46
117
118        <service
118-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-25:19
119            android:name="expo.modules.video.playbackService.ExpoVideoPlaybackService"
119-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-87
120            android:exported="false"
120-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
121            android:foregroundServiceType="mediaPlayback" >
121-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-58
122            <intent-filter>
122-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-24:29
123                <action android:name="androidx.media3.session.MediaSessionService" />
123-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-86
123-->[:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:25-83
124            </intent-filter>
125        </service>
126
127        <activity
127-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
128            android:name="com.facebook.react.devsupport.DevSettingsActivity"
128-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
129            android:exported="false" />
129-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
130
131        <provider
131-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
132            android:name="expo.modules.filesystem.FileSystemFileProvider"
132-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
133            android:authorities="com.ramsha_malik.tms_app.FileSystemFileProvider"
133-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
134            android:exported="false"
134-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
135            android:grantUriPermissions="true" >
135-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
136            <meta-data
136-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
137                android:name="android.support.FILE_PROVIDER_PATHS"
137-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
138                android:resource="@xml/file_system_provider_paths" />
138-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
139        </provider>
140
141        <meta-data
141-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
142            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
142-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
143            android:value="GlideModule" />
143-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
144
145        <provider
145-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
146            android:name="androidx.startup.InitializationProvider"
146-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
147            android:authorities="com.ramsha_malik.tms_app.androidx-startup"
147-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
148            android:exported="false" >
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
149            <meta-data
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
150                android:name="androidx.emoji2.text.EmojiCompatInitializer"
150-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
151                android:value="androidx.startup" />
151-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
152            <meta-data
152-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
153                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
153-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
154                android:value="androidx.startup" />
154-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
155            <meta-data
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
156                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
157                android:value="androidx.startup" />
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
158        </provider>
159
160        <service
160-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
161            android:name="androidx.room.MultiInstanceInvalidationService"
161-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
162            android:directBootAware="true"
162-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
163            android:exported="false" />
163-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
164
165        <receiver
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
166            android:name="androidx.profileinstaller.ProfileInstallReceiver"
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
167            android:directBootAware="false"
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
168            android:enabled="true"
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
169            android:exported="true"
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
170            android:permission="android.permission.DUMP" >
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
171            <intent-filter>
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
172                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
173            </intent-filter>
174            <intent-filter>
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
175                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
176            </intent-filter>
177            <intent-filter>
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
178                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
179            </intent-filter>
180            <intent-filter>
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
181                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
182            </intent-filter>
183        </receiver>
184    </application>
185
186</manifest>
