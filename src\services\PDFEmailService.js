import { supabase } from '../lib/supabase';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';

class PDFEmailService {
  static async generateAndSendPDF(formType, formData) {
    try {
      // Generate PDF content based on form type
      const pdfContent = await this.generatePDFContent(formType, formData);
      
      // Generate PDF file
      const { uri } = await Print.printToFileAsync({
        html: pdfContent,
        width: 612, // US Letter width in points
        height: 792, // US Letter height in points
      });

      // Prepare email content
      const adminEmail = "<EMAIL>";
      const senderName = "TMS of Emerald Coast";
      const senderEmail = "<EMAIL>";
      const resendApiKey = "re_a6sV9E4m_7z2rZVBbLQpkxbuqdLCam3DR";

      // Read the PDF file
      const pdfBase64 = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Send email with PDF attachment
      const response = await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${resendApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: `"${senderName}" <${senderEmail}>`,
          to: [adminEmail],
          subject: `New ${formType} Submission`,
          html: `<p>A new ${formType} has been submitted. Please find the details in the attached PDF.</p>`,
          attachments: [
            {
              filename: `${formType.toLowerCase().replace(/\s+/g, '_')}.pdf`,
              content: pdfBase64
            }
          ]
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.message || "Failed to send email");
      }

      // Clean up the temporary PDF file
      await FileSystem.deleteAsync(uri);

      return { success: true };
    } catch (error) {
      console.error('Error in generateAndSendPDF:', error);
      return { success: false, error: error.message };
    }
  }

  static async generatePDFContent(formType, formData) {
    // Generate HTML content based on form type
    let htmlContent = `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .section { margin-bottom: 20px; }
            .field { margin-bottom: 10px; }
            .label { font-weight: bold; }
            .value { margin-left: 10px; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; }
            .checkbox-list { margin-left: 20px; }
            .checkbox-item { margin-bottom: 5px; }
            .checkbox-selected { color: #000; }
            .checkbox-unselected { color: #999; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${formType}</h1>
            <p>Submission Date: ${new Date().toLocaleDateString()}</p>
          </div>
    `;

    // Add form-specific content
    switch (formType) {
      case 'Patient Demographics':
        htmlContent += this.generatePatientDemographicsContent(formData);
        break;
      case 'Medical History':
        htmlContent += this.generateMedicalHistoryContent(formData);
        break;
      case 'Pre-Certification Medication List':
        htmlContent += this.generatePreCertMedListContent(formData);
        break;
      case 'BDI':
        htmlContent += this.generateBDIContent(formData);
        break;
      case 'PHQ-9':
        htmlContent += this.generatePHQ9Content(formData);
        break;
    }

    htmlContent += `
          <div class="footer">
            <p>Generated by TMS of Emerald Coast</p>
          </div>
        </body>
      </html>
    `;

    return htmlContent;
  }

  static generatePatientDemographicsContent(formData) {
    return `
      <div class="section">
        <h2>Patient Demographics</h2>
        <div class="field">
          <span class="label">Full Legal Name:</span>
          <span class="value">${formData.fullLegalName || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Date:</span>
          <span class="value">${formData.date ? new Date(formData.date).toLocaleDateString() : 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Date of Birth:</span>
          <span class="value">${formData.dob ? new Date(formData.dob).toLocaleDateString() : 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Age:</span>
          <span class="value">${formData.age || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Phone:</span>
          <span class="value">${formData.phone || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Email:</span>
          <span class="value">${formData.email || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Address:</span>
          <span class="value">${formData.address || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">City, State, ZIP:</span>
          <span class="value">${formData.cityStateZip || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">SSN:</span>
          <span class="value">${formData.ssn || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Gender:</span>
          <span class="value">${formData.gender || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Active Duty Service Member:</span>
          <span class="value">${formData.activeDutyServiceMember || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">DOD Benefit:</span>
          <span class="value">${formData.dodBenefit || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Current Employer:</span>
          <span class="value">${formData.currentEmployer || 'N/A'}</span>
        </div>
      </div>

      <div class="section">
        <h2>Spouse Information</h2>
        <div class="field">
          <span class="label">Spouse Name:</span>
          <span class="value">${formData.spouseName || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Spouse Age:</span>
          <span class="value">${formData.spouseAge || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Spouse Date of Birth:</span>
          <span class="value">${formData.spouseDob ? new Date(formData.spouseDob).toLocaleDateString() : 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Spouse SSN:</span>
          <span class="value">${formData.spouseSsn || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Spouse Employer:</span>
          <span class="value">${formData.spouseEmployer || 'N/A'}</span>
        </div>
      </div>

      <div class="section">
        <h2>Medical & Insurance Information</h2>
        <div class="field">
          <span class="label">Referring Provider:</span>
          <span class="value">${formData.referringProvider || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Primary Health Insurance:</span>
          <span class="value">${formData.primaryHealthInsurance || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Policy:</span>
          <span class="value">${formData.policy || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Group Number:</span>
          <span class="value">${formData.group || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Known Medical Conditions:</span>
          <span class="value">${formData.knownMedicalConditions || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Drug Allergies:</span>
          <span class="value">${formData.drugAllergies || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Current Medications:</span>
          <span class="value">${formData.currentMedications || 'N/A'}</span>
        </div>
      </div>

      <div class="section">
        <h2>Emergency Contact</h2>
        <div class="field">
          <span class="label">Emergency Contact Name:</span>
          <span class="value">${formData.emergencyContactName || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Emergency Contact Phone:</span>
          <span class="value">${formData.emergencyContactPhone || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Emergency Contact Relationship:</span>
          <span class="value">${formData.emergencyContactRelationship || 'N/A'}</span>
        </div>
      </div>
    `;
  }

  static generateMedicalHistoryContent(formData) {
    const selectedConditions = Object.entries(formData.medicalConditions || {})
      .filter(([_, value]) => value)
      .map(([key]) => key);

    return `
      <div class="section">
        <h2>Medical History</h2>
        <div class="field">
          <span class="label">Medical Conditions:</span>
          <div class="checkbox-list">
            ${selectedConditions.length > 0 
              ? selectedConditions.map(condition => `
                  <div class="checkbox-item checkbox-selected">✓ ${condition}</div>
                `).join('')
              : '<div class="checkbox-item checkbox-unselected">None</div>'
            }
          </div>
        </div>
        <div class="field">
          <span class="label">Suicidal History:</span>
          <div class="value">
            <div>Thoughts: ${formData.suicidalThoughts || 'N/A'}</div>
            <div>Attempts: ${formData.attempts || 'N/A'}</div>
            ${formData.suicidalExplanation ? `<div>Explanation: ${formData.suicidalExplanation}</div>` : ''}
          </div>
        </div>
        <div class="field">
          <span class="label">Previous Psychiatrist/Therapist:</span>
          <span class="value">${formData.previousPsychiatrist || 'None'}</span>
        </div>
        <div class="field">
          <span class="label">Psychiatric Hospitalizations:</span>
          <span class="value">${formData.psychiatricHospitalizations || 'None'}</span>
        </div>
        <div class="field">
          <span class="label">Legal History:</span>
          <div class="value">
            <div>Charges: ${formData.legalCharges || 'N/A'}</div>
            ${formData.legalExplanation ? `<div>Explanation: ${formData.legalExplanation}</div>` : ''}
          </div>
        </div>
        <div class="field">
          <span class="label">Allergies:</span>
          <span class="value">${formData.allergies || 'None'}</span>
        </div>
        <div class="field">
          <span class="label">Family History:</span>
          <span class="value">${formData.familyHistory || 'None'}</span>
        </div>
        <div class="field">
          <span class="label">Signature:</span>
          <span class="value">${formData.signature || 'N/A'}</span>
        </div>
      </div>
    `;
  }

  static generatePreCertMedListContent(formData) {
    console.log('Pre-cert PDF generation - formData:', JSON.stringify(formData, null, 2));
    return `
      <div class="section">
        <h2>Pre-Certification Medication List</h2>
        <div class="field">
          <span class="label">Name:</span>
          <span class="value">${formData.name || 'N/A'}</span>
        </div>
        <div class="field">
          <span class="label">Date of Birth:</span>
          <span class="value">${formData.dateOfBirth ? new Date(formData.dateOfBirth).toLocaleDateString() : 'N/A'}</span>
        </div>
        <h3>Selected Medications</h3>
        ${(() => {
          const medications = formData.medications || {};
          const hasAnyMedications = Object.keys(medications).length > 0;

          if (!hasAnyMedications) {
            return '<div class="field"><span class="value">No medications selected</span></div>';
          }

          const categoryContent = Object.entries(medications).map(([category, medications]) => {
            const selectedMeds = Object.entries(medications)
              .filter(([_, details]) => details && details.selected)
              .map(([medName, details]) => {
                return `
                  <div class="field">
                    <span class="label">${medName}:</span>
                    <div class="value">
                      <div><strong>Dose:</strong> ${details.dosage || 'Not specified'}</div>
                      <div><strong>Start Date:</strong> ${details.startDate ? new Date(details.startDate).toLocaleDateString() : 'Not specified'}</div>
                      <div><strong>End Date:</strong> ${details.endDate ? new Date(details.endDate).toLocaleDateString() : 'Not specified'}</div>
                      <div><strong>Reason for Discontinuing:</strong> ${details.reasonForDiscontinuing || 'Not specified'}</div>
                    </div>
                  </div>
                `;
              }).join('');

            return selectedMeds ? `
              <div class="section">
                <h4>${category}</h4>
                ${selectedMeds}
              </div>
            ` : '';
          }).join('');

          return categoryContent || '<div class="field"><span class="value">No medications selected</span></div>';
        })()}
      </div>
    `;
  }

  static generateBDIContent(formData) {
    const responses = formData.responses || {};
    const totalScore = formData.totalScore || Object.values(responses).reduce((sum, value) => {
      // Handle special cases for sleep and appetite questions
      if (typeof value === 'string' && value.includes('a')) {
        return sum + parseInt(value.replace(/[a-z]/g, ''));
      }
      return sum + parseInt(value || 0);
    }, 0);

    // BDI response options for displaying actual text
    const bdiOptions = {
      0: [ // Sadness
        { label: "0. I do not feel sad.", value: "0" },
        { label: "1. I feel sad much of the time.", value: "1" },
        { label: "2. I am sad all the time.", value: "2" },
        { label: "3. I am so sad or unhappy that I can't stand it.", value: "3" }
      ],
      1: [ // Pessimism
        { label: "0. I am not discouraged about my future.", value: "0" },
        { label: "1. I feel more discouraged about my future than I used to.", value: "1" },
        { label: "2. I do not expect things to work out for me.", value: "2" },
        { label: "3. I feel my future is hopeless and will only get worse.", value: "3" }
      ],
      2: [ // Past Failure
        { label: "0. I do not feel like a failure.", value: "0" },
        { label: "1. I have failed more than I should have.", value: "1" },
        { label: "2. As I look back, I see a lot of failures.", value: "2" },
        { label: "3. I feel I am a total failure as a person.", value: "3" }
      ],
      3: [ // Loss of Pleasure
        { label: "0. I get as much pleasure as I ever did from the things I enjoy.", value: "0" },
        { label: "1. I don't enjoy things as much as I used to.", value: "1" },
        { label: "2. I get very little pleasure from the things I used to enjoy.", value: "2" },
        { label: "3. I can't get any pleasure from the things I used to enjoy.", value: "3" }
      ],
      4: [ // Guilty Feelings
        { label: "0. I don't feel particularly guilty.", value: "0" },
        { label: "1. I feel guilty over many things I have done or should have done.", value: "1" },
        { label: "2. I feel quite guilty most of the time.", value: "2" },
        { label: "3. I feel guilty all of the time.", value: "3" }
      ],
      5: [ // Punishment Feelings
        { label: "0. I don't feel I am being punished.", value: "0" },
        { label: "1. I feel I may be punished.", value: "1" },
        { label: "2. I expect to be punished.", value: "2" },
        { label: "3. I feel I am being punished.", value: "3" }
      ],
      6: [ // Self-Dislike
        { label: "0. I feel the same about myself as ever.", value: "0" },
        { label: "1. I have lost confidence in myself.", value: "1" },
        { label: "2. I am disappointed in myself.", value: "2" },
        { label: "3. I dislike myself.", value: "3" }
      ],
      7: [ // Self-Criticalness
        { label: "0. I don't criticize or blame myself more than usual.", value: "0" },
        { label: "1. I am more critical of myself than I used to be.", value: "1" },
        { label: "2. I criticize myself for all of my faults.", value: "2" },
        { label: "3. I blame myself for everything bad that happens.", value: "3" }
      ],
      8: [ // Suicidal Thoughts or Wishes
        { label: "0. I don't have any thoughts of killing myself.", value: "0" },
        { label: "1. I have thoughts of killing myself, but I would not carry them out.", value: "1" },
        { label: "2. I would like to kill myself.", value: "2" },
        { label: "3. I would kill myself if I had the chance.", value: "3" }
      ],
      9: [ // Crying
        { label: "0. I don't cry anymore than I used to.", value: "0" },
        { label: "1. I cry more than I used to.", value: "1" },
        { label: "2. I cry over every little thing.", value: "2" },
        { label: "3. I feel like crying, but I can't.", value: "3" }
      ],
      10: [ // Agitation
        { label: "0. I am no more restless or wound up than usual.", value: "0" },
        { label: "1. I feel more restless or wound up than usual.", value: "1" },
        { label: "2. I am so restless or agitated, it's hard to stay still.", value: "2" },
        { label: "3. I am so restless or agitated that I have to keep moving or doing something.", value: "3" }
      ],
      11: [ // Loss of Interest
        { label: "0. I have not lost interest in other people or activities.", value: "0" },
        { label: "1. I am less interested in other people or things than before.", value: "1" },
        { label: "2. I have lost most of my interest in other people or things.", value: "2" },
        { label: "3. It's hard to get interested in anything.", value: "3" }
      ],
      12: [ // Indecisiveness
        { label: "0. I make decisions about as well as ever.", value: "0" },
        { label: "1. I find it more difficult to make decisions than usual.", value: "1" },
        { label: "2. I have much greater difficulty in making decisions than I used to.", value: "2" },
        { label: "3. I have trouble making any decisions.", value: "3" }
      ],
      13: [ // Worthlessness
        { label: "0. I do not feel I am worthless.", value: "0" },
        { label: "1. I don't consider myself as worthwhile and useful as I used to.", value: "1" },
        { label: "2. I feel more worthless as compared to others.", value: "2" },
        { label: "3. I feel utterly worthless.", value: "3" }
      ],
      14: [ // Loss of Energy
        { label: "0. I have as much energy as ever.", value: "0" },
        { label: "1. I have less energy than I used to have.", value: "1" },
        { label: "2. I don't have enough energy to do very much.", value: "2" },
        { label: "3. I don't have enough energy to do anything.", value: "3" }
      ],
      15: [ // Changes in Sleeping Pattern
        { label: "0. I have not experienced any change in my sleeping.", value: "0" },
        { label: "1a. I sleep somewhat more than usual.", value: "1a" },
        { label: "1b. I sleep somewhat less than usual.", value: "1b" },
        { label: "2a. I sleep a lot more than usual.", value: "2a" },
        { label: "2b. I sleep a lot less than usual.", value: "2b" },
        { label: "3a. I sleep most of the day.", value: "3a" },
        { label: "3b. I wake up 1-2 hours early and can't get back to sleep.", value: "3b" }
      ],
      16: [ // Irritability
        { label: "0. I am not more irritable than usual.", value: "0" },
        { label: "1. I am more irritable than usual.", value: "1" },
        { label: "2. I am much more irritable than usual.", value: "2" },
        { label: "3. I am irritable all the time.", value: "3" }
      ],
      17: [ // Changes in Appetite
        { label: "0. I have not experienced any change in my appetite.", value: "0" },
        { label: "1a. My appetite is somewhat less than usual.", value: "1a" },
        { label: "1b. My appetite is somewhat greater than usual.", value: "1b" },
        { label: "2a. My appetite is much less than before.", value: "2a" },
        { label: "2b. My appetite is much greater than usual.", value: "2b" },
        { label: "3a. I have no appetite at all.", value: "3a" },
        { label: "3b. I crave food all the time.", value: "3b" }
      ],
      18: [ // Concentration Difficulty
        { label: "0. I can concentrate as well as ever.", value: "0" },
        { label: "1. I can't concentrate as well as usual.", value: "1" },
        { label: "2. It's hard to keep my mind on anything for very long.", value: "2" },
        { label: "3. I find I can't concentrate on anything.", value: "3" }
      ],
      19: [ // Tiredness or Fatigue
        { label: "0. I am no more tired or fatigued than usual.", value: "0" },
        { label: "1. I get more tired or fatigued more easily than usual.", value: "1" },
        { label: "2. I am too tired or fatigued to do a lot of the things I used to do.", value: "2" },
        { label: "3. I am too tired or fatigued to do most of the things I used to do", value: "3" }
      ],
      20: [ // Loss of Interest in Sex
        { label: "0. I have not noticed any recent change in my interest in sex.", value: "0" },
        { label: "1. I am less interested in sex than I used to be.", value: "1" },
        { label: "2. I am much less interested in sex now.", value: "2" },
        { label: "3. I have lost interest in sex completely.", value: "3" }
      ]
    };

    return `
      <div class="section">
        <h2>Beck Depression Inventory (BDI-II)</h2>
        <div class="field">
          <span class="label">Total Score:</span>
          <span class="value">${totalScore}</span>
        </div>
        <h3>Responses</h3>
        ${Object.entries(responses).map(([questionIndex, response]) => {
          const questionNumber = parseInt(questionIndex) + 1;
          const questionText = bdiQuestions[questionIndex];
          const options = bdiOptions[questionIndex] || [];
          const selectedOption = options.find(opt => opt.value === response);
          const responseText = selectedOption ? selectedOption.label : `${response || 'N/A'}`;

          return `
            <div class="field">
              <span class="label">Question ${questionNumber}: ${questionText}</span>
              <span class="value">${responseText}</span>
            </div>
          `;
        }).join('')}
      </div>
    `;
  }

  static generatePHQ9Content(formData) {
    const responses = formData.responses || {};
    const totalScore = formData.totalScore || Object.values(responses).reduce((sum, value) => sum + parseInt(value || 0), 0);

    // PHQ-9 response options for displaying actual text
    const phq9Options = [
      "0. Not at all",
      "1. Several days",
      "2. More than half the days",
      "3. Nearly every day"
    ];

    return `
      <div class="section">
        <h2>Patient Health Questionnaire (PHQ-9)</h2>
        <div class="field">
          <span class="label">Total Score:</span>
          <span class="value">${totalScore}</span>
        </div>
        <h3>Responses</h3>
        ${Object.entries(responses).map(([questionIndex, response]) => {
          const questionNumber = parseInt(questionIndex) + 1;
          const questionText = phq9Questions[questionIndex];
          const responseValue = parseInt(response || 0);
          const responseText = phq9Options[responseValue] || `${response || 'N/A'}`;

          return `
            <div class="field">
              <span class="label">Question ${questionNumber}: ${questionText}</span>
              <span class="value">${responseText}</span>
            </div>
          `;
        }).join('')}
      </div>
    `;
  }
}

// Add question arrays for BDI and PHQ-9
const bdiQuestions = [
  "Sadness", "Pessimism", "Past Failure", "Loss of Pleasure", "Guilty Feelings",
  "Punishment Feelings", "Self-Dislike", "Self-Criticalness", "Suicidal Thoughts or Wishes",
  "Crying", "Agitation", "Loss of Interest", "Indecisiveness", "Worthlessness",
  "Loss of Energy", "Changes in Sleeping Pattern", "Irritability", "Changes in Appetite",
  "Concentration Difficulty", "Tiredness or Fatigue", "Loss of Interest in Sex"
];

const phq9Questions = [
  "Little interest or pleasure in doing things",
  "Feeling down, depressed, or hopeless",
  "Trouble falling or staying asleep, or sleeping too much",
  "Feeling tired or having little energy",
  "Poor appetite or overeating",
  "Feeling bad about yourself",
  "Trouble concentrating on things",
  "Moving or speaking so slowly that other people could have noticed",
  "Thoughts that you would be better off dead or of hurting yourself"
];

export default PDFEmailService; 