/**
 * App color constants
 */
export default {
  primary: '#2c5264',
  secondary: '#10a39b',
  background: '#fff',
  text: '#333',
  lightText: '#999',
  white: 'white',
  black: '#1a1a1a',
  gray: '#eee',
  lightGray: '#f5f5f5',
  darkGray: '#666',
  error: '#D32F2F',
  success: '#52c41a',
  accent: '#ffe082',
  transparent: 'transparent',
  overlay: 'rgba(0,0,0,0.42)',
  formBackground: 'rgba(24, 32, 36, 0.85)',
  
  // Enhanced overlay colors for better contrast
  heroOverlay: 'rgba(44, 82, 100, 0.65)', // Darker for better text contrast
  heroOverlayLight: 'rgba(44, 82, 100, 0.45)', // Alternative lighter option
  contactOverlay: 'rgba(15, 25, 35, 0.85)', // Better contrast for contact cards
  playButtonOverlay: 'rgba(44, 82, 100, 0.9)', // Better contrast for play buttons
  
  // High contrast text colors for overlays
  overlayText: '#ffffff',
  overlayTextSecondary: '#f0f0f0',
  overlayTextAccent: '#4db3c9',
  
  // Accessible color combinations
  accessiblePrimary: '#1e3a47', // Darker primary for better contrast
  accessibleSecondary: '#0d8a82', // Darker secondary for better contrast
};
