/**
 * Consistent spacing constants for all screens
 */
const Spacing = {
  // Standard section spacing
  HERO_TO_SECTION: 32,      // Space between hero section and first content section
  SECTION_TO_SECTION: 50,   // Space between content sections
  
  // Additional spacing options for specific use cases
  FORM_INTERNAL: 16,        // Space between form elements
  CARD_INTERNAL: 20,        // Space inside cards/containers
  TEXT_SPACING: 8,          // Space between text elements
  BUTTON_SPACING: 12,       // Space around buttons
};

export default Spacing; 