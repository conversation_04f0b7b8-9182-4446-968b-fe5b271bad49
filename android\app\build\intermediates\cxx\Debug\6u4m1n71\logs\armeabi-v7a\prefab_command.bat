@echo off
"C:\\Program Files\\JDK_Adoptium_21LTS\\jdk-21.0.6+7\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  armeabi-v7a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging8808952420745749635\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd2f07ca05732640440e3b4e0b986029\\transformed\\react-android-0.79.2-debug\\prefab" ^
  "C:\\Users\\<USER>\\Downloads\\tms_application\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\432cr1w5" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7dfe0737bb4b60145b93a39cf3cf83a6\\transformed\\hermes-android-0.79.2-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aae2e161ad8460d34e386c90489cc2b4\\transformed\\fbjni-0.7.0\\prefab"
