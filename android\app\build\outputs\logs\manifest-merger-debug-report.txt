-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:1:1-33:12
MERGED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:1:1-33:12
INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:expo] C:\Users\<USER>\Downloads\tms_application\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [:expo-updates] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-28:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a130a8f17cb144401c60add18097dcf9\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:2:1-19:12
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\34e13e237df48afadea3b177a159f330\transformed\expo.modules.splashscreen-0.30.8\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\428bacad40a6f9fbc888386871dcda9e\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [:expo-structured-headers] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\afbbcd6a450aa139608eb98a5c17e4b5\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fc8b596b255ae8c98ca5573e02e2411\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bbf8a269e5a7d0b0f9afce42f29f59e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f4b946ca4709998682f5290feede2c6\transformed\glide-plugin-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ace0de5ee8c19ba389bcafcdcd6c6ec1\transformed\awebp-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d87c8a515546943f359f1c909351ec8\transformed\apng-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\263a9a41c0767b45cea635ab9a77cd78\transformed\gif-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d651171d8687570f4efa73451faf338\transformed\avif-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\73c48d20dedefa9986b4d7194881229d\transformed\frameanimation-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bce72c8d6c2b594d45298a87e02b9e4\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-33:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3495132de0139c9248c0634696445e26\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea2e21d4b40d526af4a7330e568a944\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc6268b713348b2c2befd90d45d5efab\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a227b17fcd6955f43f45d4812ce8033\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5f2105f833d3bdd73d664a97fdc4106\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\74c11b1adb57be73089429ecf3a62add\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d3acb75251a79c8ec5e4c6fb586381c\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\510722da29067cebaee8678f67adf53d\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5b08d223bfd652d8c1cf1c9451b4a75\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20270f8d5726f3e72f93d17aff2de15e\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c535970443e12d53c18d8fb7180b93c4\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df8de6527224c6eb10d2b0f6655c1c37\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ae31ddf93d1e5c51782afd30bfa293\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce0b6a21885d314ccad7bed0d1a3a8d6\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a24959d0179993f7cf8f8265187787a\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fdf4108ec0d99b7d8c38682cd7feb55\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed43af2edabcaecd526b66132228605a\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\910fdfcb994c7e154a942758db93c372\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b219e5973ad86b7441645cca36afd7b3\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\edfd6a89a7e25e2dda771e307af88655\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9694f2038ddc543510f5eac1b9e7a1a\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cd3d3591fb00b2f9660c8739b23af4e\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b00bd319b85e63f4d592685b44cde56e\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b761f9eeeceaba9070ef5feb959a8c89\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ce2284e1e6da2814f5740316c709611\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\748b638882af0576e7a6860a79155738\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85061fa255e7b68a017f0b4ef25dafd2\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c91b6d2c9bbd541703f3b460020d515\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ebad0bc286cf685494bb4dd14790223a\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa4da4d9e806d4b321577ae8a396e457\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a80d11bb86f875a17eb6af95e5d021\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce0328857accb91cf36a3e0f9d61e950\transformed\avif-integration-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60fd552d88db9bc186f9930d4da26f25\transformed\glide-transformations-4.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\774615a759e0ba61d61c69d51912e48a\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac62e155fc0166728199cc6ba3a1aa7\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75504bd3640385a5ec16738c32ca2058\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e5f58c5bcccbfa439b66f27bd15e544\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4abc979e46c3a1c347b20026115e8c3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6836fe3e8a5acd55f818be21140e6fb3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2c076c9cbc64a7d610972118422819f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2af142e7b492cf9471d6527e746afa5c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f98864ceea75e04d112cc2736754a942\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41c295ecdf8bbb540e5aa464ed9f98c4\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d57699f3eb162dde04aba764d9f38f04\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f7996bce1dd550dfb6c254808c8db0c\transformed\media3-ui-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6555837f9e89f159f8d23cd4cae04dcc\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca9d68e9ead352ec88e037bc3163a3c8\transformed\media3-datasource-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\418a30c6831dcf98155937ed7c50e735\transformed\media3-extractor-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7889cf232a2ad41bcd4b8a61ea4c7607\transformed\media3-container-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a00a51f9f86d9a6a8f077b6a09ef3b27\transformed\media3-decoder-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\189a9ec51974f9c24843118f7a3a6c66\transformed\media3-database-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf23199231df0aff7eb9e682f02ab2f2\transformed\media3-common-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-session:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29b9c3bc2763a8fc1ca54c4fbd792047\transformed\media3-session-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55047732022e8794aa5d3acb9690614f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8aef8412c7fd6be10476e9eac7c7a948\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c5f0450ba204e734b05f226554b670a\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4523192ad92a9c3d828997a6e33cfcd0\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b2a88c043fa331955438c8277360ab9\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\affe7de1ac50ea99304d18035308f511\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f4c02fea9e4d396073772dffdbe0024\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a0b94e2d070cecbb743d301c8131002\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03c89ee756245d67460cf9a312958e25\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44bc66389f11cbc3f4f1f10102d22906\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a21e9970dce3defffb3381a209207c49\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ff82f66f00c0a448d42fdfc3177c2b\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c769dd71d0a4010028e8d58f776bd7df\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99b467659dbf138fd89efc85b6252007\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\caa1aace09bd06445050846a2d8d9033\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\befd3b786c1753c5da35d249fb278bfb\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\83fb30c725d782917068d185e19d63b2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3ac9d4823f8dbdab4ffb53c2a6f71fa\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3767d29164fb986bd46212528a6afad6\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f32ee84e4eac66bd29565e070602426e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d96be588365241022bddada7f275a0b\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-constants] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-eas-client] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linking] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e738c77a6b9c7603924db86bbb48b9fa\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fda2ff3650bc997275df86232fc0a479\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2de63f23279b53619ee5b6e3d105436\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a20b84f0501516deb7ef573ed6fa42d\transformed\expo.modules.lineargradient-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\538f56bd6702d7f851974ade07f1d3d0\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d590517ffbbc1342bd47e61c5be20d2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d236445d4628779bcb002ed47832753\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9977a7d2878a7cf4e601806aa78e5d60\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\555a09d762726520bd479a537117a98c\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e682df33658d676c6aa49ecaf1fc3319\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\185ce690c716ca9fa4934371083c8ad5\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eee174d8b39b4a7f8ebecb54f1fa2903\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1314a050662208409175ca56510a64f\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dfe0737bb4b60145b93a39cf3cf83a6\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecfe6108001d232de69077d44b5cc6f6\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcbbfb7950778a034e0af409c780b0a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf7a1beddb95217615a270a482c9ad1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\63fc9732702a152d6d39b0864d8bb7a6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f331e97afc05724e0d2c93117b55dd3\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0861179c3d4484199e266505b04edb9\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66b2842913ecd72da38bcd277035743b\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caef0edd87b2553422cab6e2533dd954\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\413e4ee8b74c2d4b232dfb69e11fdd00\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\008d61dc850a31a32ffff6ecb280623e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae2e161ad8460d34e386c90489cc2b4\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb9a8dbdb99297f78104287ff61f6e87\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\db21de1fe14fa7fda874facb8070173c\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb094afa4d3e0c131e710724513c94f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:2:3-64
MERGED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:7:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:2:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:3:3-77
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:15:5-17:38
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:15:5-17:38
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
	android:maxSdkVersion
		ADDED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:3:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:4:3-75
MERGED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:4:3-75
MERGED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:4:3-75
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:4:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:5:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fda2ff3650bc997275df86232fc0a479\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fda2ff3650bc997275df86232fc0a479\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:5:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:6:3-78
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:6:20-76
queries
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:7:3-13:13
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:8:5-12:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:7-58
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:7-67
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:17-65
data
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:7-37
	android:scheme
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:13-35
application
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:3-32:17
MERGED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:3-32:17
MERGED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:3-32:17
INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-26:19
MERGED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-26:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\428bacad40a6f9fbc888386871dcda9e\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\428bacad40a6f9fbc888386871dcda9e\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bbf8a269e5a7d0b0f9afce42f29f59e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bbf8a269e5a7d0b0f9afce42f29f59e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d590517ffbbc1342bd47e61c5be20d2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d590517ffbbc1342bd47e61c5be20d2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\63fc9732702a152d6d39b0864d8bb7a6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\63fc9732702a152d6d39b0864d8bb7a6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb9a8dbdb99297f78104287ff61f6e87\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb9a8dbdb99297f78104287ff61f6e87\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb094afa4d3e0c131e710724513c94f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb094afa4d3e0c131e710724513c94f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:5:5-6:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:221-247
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:221-247
	android:label
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:48-80
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:48-80
	tools:ignore
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:116-161
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:116-161
	tools:targetApi
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:icon
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:81-115
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:81-115
	android:allowBackup
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:162-188
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:162-188
	android:theme
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:189-220
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:189-220
	tools:replace
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:16-47
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:15:5-82
	android:value
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:15:60-80
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:15:16-59
meta-data#expo.modules.updates.EXPO_RUNTIME_VERSION
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:16:5-119
	android:value
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:16:73-117
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:16:16-72
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:17:5-105
	android:value
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:17:81-103
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:17:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:18:5-99
	android:value
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:18:80-97
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:18:16-79
meta-data#expo.modules.updates.EXPO_UPDATE_URL
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:19:5-141
	android:value
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:19:68-139
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:19:16-67
activity#com.ramsha_malik.tms_app.MainActivity
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:5-31:16
	android:screenOrientation
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:280-316
	android:launchMode
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:135-166
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:167-209
	android:exported
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:256-279
	android:configChanges
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:44-134
	android:theme
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:210-255
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:21:7-24:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:22:9-60
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:22:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:23:9-68
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:23:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:tmsapp
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:25:7-30:23
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:27:9-67
	android:name
		ADDED from C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:27:19-65
uses-sdk
INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\Downloads\tms_application\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\Downloads\tms_application\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-updates] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a130a8f17cb144401c60add18097dcf9\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a130a8f17cb144401c60add18097dcf9\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:4:5-44
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:4:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\34e13e237df48afadea3b177a159f330\transformed\expo.modules.splashscreen-0.30.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\34e13e237df48afadea3b177a159f330\transformed\expo.modules.splashscreen-0.30.8\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\428bacad40a6f9fbc888386871dcda9e\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\428bacad40a6f9fbc888386871dcda9e\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [:expo-structured-headers] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-structured-headers] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\afbbcd6a450aa139608eb98a5c17e4b5\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\afbbcd6a450aa139608eb98a5c17e4b5\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fc8b596b255ae8c98ca5573e02e2411\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fc8b596b255ae8c98ca5573e02e2411\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bbf8a269e5a7d0b0f9afce42f29f59e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bbf8a269e5a7d0b0f9afce42f29f59e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f4b946ca4709998682f5290feede2c6\transformed\glide-plugin-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f4b946ca4709998682f5290feede2c6\transformed\glide-plugin-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ace0de5ee8c19ba389bcafcdcd6c6ec1\transformed\awebp-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ace0de5ee8c19ba389bcafcdcd6c6ec1\transformed\awebp-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d87c8a515546943f359f1c909351ec8\transformed\apng-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d87c8a515546943f359f1c909351ec8\transformed\apng-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\263a9a41c0767b45cea635ab9a77cd78\transformed\gif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\263a9a41c0767b45cea635ab9a77cd78\transformed\gif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d651171d8687570f4efa73451faf338\transformed\avif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d651171d8687570f4efa73451faf338\transformed\avif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\73c48d20dedefa9986b4d7194881229d\transformed\frameanimation-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\73c48d20dedefa9986b4d7194881229d\transformed\frameanimation-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bce72c8d6c2b594d45298a87e02b9e4\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bce72c8d6c2b594d45298a87e02b9e4\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3495132de0139c9248c0634696445e26\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3495132de0139c9248c0634696445e26\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea2e21d4b40d526af4a7330e568a944\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea2e21d4b40d526af4a7330e568a944\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc6268b713348b2c2befd90d45d5efab\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc6268b713348b2c2befd90d45d5efab\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a227b17fcd6955f43f45d4812ce8033\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a227b17fcd6955f43f45d4812ce8033\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5f2105f833d3bdd73d664a97fdc4106\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5f2105f833d3bdd73d664a97fdc4106\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\74c11b1adb57be73089429ecf3a62add\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\74c11b1adb57be73089429ecf3a62add\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d3acb75251a79c8ec5e4c6fb586381c\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d3acb75251a79c8ec5e4c6fb586381c\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\510722da29067cebaee8678f67adf53d\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\510722da29067cebaee8678f67adf53d\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5b08d223bfd652d8c1cf1c9451b4a75\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5b08d223bfd652d8c1cf1c9451b4a75\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20270f8d5726f3e72f93d17aff2de15e\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20270f8d5726f3e72f93d17aff2de15e\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c535970443e12d53c18d8fb7180b93c4\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c535970443e12d53c18d8fb7180b93c4\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df8de6527224c6eb10d2b0f6655c1c37\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df8de6527224c6eb10d2b0f6655c1c37\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ae31ddf93d1e5c51782afd30bfa293\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ae31ddf93d1e5c51782afd30bfa293\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce0b6a21885d314ccad7bed0d1a3a8d6\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce0b6a21885d314ccad7bed0d1a3a8d6\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a24959d0179993f7cf8f8265187787a\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a24959d0179993f7cf8f8265187787a\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fdf4108ec0d99b7d8c38682cd7feb55\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fdf4108ec0d99b7d8c38682cd7feb55\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed43af2edabcaecd526b66132228605a\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed43af2edabcaecd526b66132228605a\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\910fdfcb994c7e154a942758db93c372\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\910fdfcb994c7e154a942758db93c372\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b219e5973ad86b7441645cca36afd7b3\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b219e5973ad86b7441645cca36afd7b3\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\edfd6a89a7e25e2dda771e307af88655\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\edfd6a89a7e25e2dda771e307af88655\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9694f2038ddc543510f5eac1b9e7a1a\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9694f2038ddc543510f5eac1b9e7a1a\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cd3d3591fb00b2f9660c8739b23af4e\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cd3d3591fb00b2f9660c8739b23af4e\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b00bd319b85e63f4d592685b44cde56e\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b00bd319b85e63f4d592685b44cde56e\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b761f9eeeceaba9070ef5feb959a8c89\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b761f9eeeceaba9070ef5feb959a8c89\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ce2284e1e6da2814f5740316c709611\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ce2284e1e6da2814f5740316c709611\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\748b638882af0576e7a6860a79155738\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\748b638882af0576e7a6860a79155738\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85061fa255e7b68a017f0b4ef25dafd2\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85061fa255e7b68a017f0b4ef25dafd2\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c91b6d2c9bbd541703f3b460020d515\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c91b6d2c9bbd541703f3b460020d515\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ebad0bc286cf685494bb4dd14790223a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ebad0bc286cf685494bb4dd14790223a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa4da4d9e806d4b321577ae8a396e457\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa4da4d9e806d4b321577ae8a396e457\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a80d11bb86f875a17eb6af95e5d021\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a80d11bb86f875a17eb6af95e5d021\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce0328857accb91cf36a3e0f9d61e950\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce0328857accb91cf36a3e0f9d61e950\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60fd552d88db9bc186f9930d4da26f25\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60fd552d88db9bc186f9930d4da26f25\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\774615a759e0ba61d61c69d51912e48a\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\774615a759e0ba61d61c69d51912e48a\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac62e155fc0166728199cc6ba3a1aa7\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac62e155fc0166728199cc6ba3a1aa7\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75504bd3640385a5ec16738c32ca2058\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75504bd3640385a5ec16738c32ca2058\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e5f58c5bcccbfa439b66f27bd15e544\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e5f58c5bcccbfa439b66f27bd15e544\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4abc979e46c3a1c347b20026115e8c3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4abc979e46c3a1c347b20026115e8c3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6836fe3e8a5acd55f818be21140e6fb3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6836fe3e8a5acd55f818be21140e6fb3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2c076c9cbc64a7d610972118422819f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2c076c9cbc64a7d610972118422819f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2af142e7b492cf9471d6527e746afa5c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2af142e7b492cf9471d6527e746afa5c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f98864ceea75e04d112cc2736754a942\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f98864ceea75e04d112cc2736754a942\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41c295ecdf8bbb540e5aa464ed9f98c4\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41c295ecdf8bbb540e5aa464ed9f98c4\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d57699f3eb162dde04aba764d9f38f04\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d57699f3eb162dde04aba764d9f38f04\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f7996bce1dd550dfb6c254808c8db0c\transformed\media3-ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f7996bce1dd550dfb6c254808c8db0c\transformed\media3-ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6555837f9e89f159f8d23cd4cae04dcc\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6555837f9e89f159f8d23cd4cae04dcc\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca9d68e9ead352ec88e037bc3163a3c8\transformed\media3-datasource-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca9d68e9ead352ec88e037bc3163a3c8\transformed\media3-datasource-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\418a30c6831dcf98155937ed7c50e735\transformed\media3-extractor-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\418a30c6831dcf98155937ed7c50e735\transformed\media3-extractor-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7889cf232a2ad41bcd4b8a61ea4c7607\transformed\media3-container-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7889cf232a2ad41bcd4b8a61ea4c7607\transformed\media3-container-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a00a51f9f86d9a6a8f077b6a09ef3b27\transformed\media3-decoder-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a00a51f9f86d9a6a8f077b6a09ef3b27\transformed\media3-decoder-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\189a9ec51974f9c24843118f7a3a6c66\transformed\media3-database-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\189a9ec51974f9c24843118f7a3a6c66\transformed\media3-database-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf23199231df0aff7eb9e682f02ab2f2\transformed\media3-common-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf23199231df0aff7eb9e682f02ab2f2\transformed\media3-common-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29b9c3bc2763a8fc1ca54c4fbd792047\transformed\media3-session-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29b9c3bc2763a8fc1ca54c4fbd792047\transformed\media3-session-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55047732022e8794aa5d3acb9690614f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55047732022e8794aa5d3acb9690614f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8aef8412c7fd6be10476e9eac7c7a948\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8aef8412c7fd6be10476e9eac7c7a948\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c5f0450ba204e734b05f226554b670a\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c5f0450ba204e734b05f226554b670a\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4523192ad92a9c3d828997a6e33cfcd0\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4523192ad92a9c3d828997a6e33cfcd0\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b2a88c043fa331955438c8277360ab9\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b2a88c043fa331955438c8277360ab9\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\affe7de1ac50ea99304d18035308f511\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\affe7de1ac50ea99304d18035308f511\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f4c02fea9e4d396073772dffdbe0024\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f4c02fea9e4d396073772dffdbe0024\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a0b94e2d070cecbb743d301c8131002\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a0b94e2d070cecbb743d301c8131002\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03c89ee756245d67460cf9a312958e25\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03c89ee756245d67460cf9a312958e25\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44bc66389f11cbc3f4f1f10102d22906\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44bc66389f11cbc3f4f1f10102d22906\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a21e9970dce3defffb3381a209207c49\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a21e9970dce3defffb3381a209207c49\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ff82f66f00c0a448d42fdfc3177c2b\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ff82f66f00c0a448d42fdfc3177c2b\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c769dd71d0a4010028e8d58f776bd7df\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c769dd71d0a4010028e8d58f776bd7df\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99b467659dbf138fd89efc85b6252007\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99b467659dbf138fd89efc85b6252007\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\caa1aace09bd06445050846a2d8d9033\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\caa1aace09bd06445050846a2d8d9033\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\befd3b786c1753c5da35d249fb278bfb\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\befd3b786c1753c5da35d249fb278bfb\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\83fb30c725d782917068d185e19d63b2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\83fb30c725d782917068d185e19d63b2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3ac9d4823f8dbdab4ffb53c2a6f71fa\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3ac9d4823f8dbdab4ffb53c2a6f71fa\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3767d29164fb986bd46212528a6afad6\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3767d29164fb986bd46212528a6afad6\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f32ee84e4eac66bd29565e070602426e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f32ee84e4eac66bd29565e070602426e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d96be588365241022bddada7f275a0b\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d96be588365241022bddada7f275a0b\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e738c77a6b9c7603924db86bbb48b9fa\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e738c77a6b9c7603924db86bbb48b9fa\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fda2ff3650bc997275df86232fc0a479\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fda2ff3650bc997275df86232fc0a479\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2de63f23279b53619ee5b6e3d105436\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2de63f23279b53619ee5b6e3d105436\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a20b84f0501516deb7ef573ed6fa42d\transformed\expo.modules.lineargradient-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a20b84f0501516deb7ef573ed6fa42d\transformed\expo.modules.lineargradient-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\538f56bd6702d7f851974ade07f1d3d0\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\538f56bd6702d7f851974ade07f1d3d0\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d590517ffbbc1342bd47e61c5be20d2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d590517ffbbc1342bd47e61c5be20d2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d236445d4628779bcb002ed47832753\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d236445d4628779bcb002ed47832753\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9977a7d2878a7cf4e601806aa78e5d60\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9977a7d2878a7cf4e601806aa78e5d60\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\555a09d762726520bd479a537117a98c\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\555a09d762726520bd479a537117a98c\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e682df33658d676c6aa49ecaf1fc3319\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e682df33658d676c6aa49ecaf1fc3319\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\185ce690c716ca9fa4934371083c8ad5\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\185ce690c716ca9fa4934371083c8ad5\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eee174d8b39b4a7f8ebecb54f1fa2903\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eee174d8b39b4a7f8ebecb54f1fa2903\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1314a050662208409175ca56510a64f\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1314a050662208409175ca56510a64f\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dfe0737bb4b60145b93a39cf3cf83a6\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dfe0737bb4b60145b93a39cf3cf83a6\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecfe6108001d232de69077d44b5cc6f6\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecfe6108001d232de69077d44b5cc6f6\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcbbfb7950778a034e0af409c780b0a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcbbfb7950778a034e0af409c780b0a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf7a1beddb95217615a270a482c9ad1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf7a1beddb95217615a270a482c9ad1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\63fc9732702a152d6d39b0864d8bb7a6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\63fc9732702a152d6d39b0864d8bb7a6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f331e97afc05724e0d2c93117b55dd3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f331e97afc05724e0d2c93117b55dd3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0861179c3d4484199e266505b04edb9\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0861179c3d4484199e266505b04edb9\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66b2842913ecd72da38bcd277035743b\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66b2842913ecd72da38bcd277035743b\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caef0edd87b2553422cab6e2533dd954\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caef0edd87b2553422cab6e2533dd954\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\413e4ee8b74c2d4b232dfb69e11fdd00\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\413e4ee8b74c2d4b232dfb69e11fdd00\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\008d61dc850a31a32ffff6ecb280623e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\008d61dc850a31a32ffff6ecb280623e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae2e161ad8460d34e386c90489cc2b4\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae2e161ad8460d34e386c90489cc2b4\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb9a8dbdb99297f78104287ff61f6e87\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb9a8dbdb99297f78104287ff61f6e87\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\db21de1fe14fa7fda874facb8070173c\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\db21de1fe14fa7fda874facb8070173c\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb094afa4d3e0c131e710724513c94f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb094afa4d3e0c131e710724513c94f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:4:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:expo-updates] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:expo-updates] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:12:5-79
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df35d449152301dd2dafd4404069518\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:12:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d57699f3eb162dde04aba764d9f38f04\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d57699f3eb162dde04aba764d9f38f04\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf23199231df0aff7eb9e682f02ab2f2\transformed\media3-common-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf23199231df0aff7eb9e682f02ab2f2\transformed\media3-common-1.4.0\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
	android:name
		ADDED from [:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb9a8dbdb99297f78104287ff61f6e87\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb9a8dbdb99297f78104287ff61f6e87\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb9a8dbdb99297f78104287ff61f6e87\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
	android:name
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-92
	android:name
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-89
activity#expo.modules.video.FullscreenPlayerActivity
ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:49
	android:supportsPictureInPicture
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-52
	android:configChanges
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-91
	android:theme
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-46
	android:name
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-71
service#expo.modules.video.playbackService.ExpoVideoPlaybackService
ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-25:19
	android:exported
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
	android:foregroundServiceType
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-58
	android:name
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-87
intent-filter#action:name:androidx.media3.session.MediaSessionService
ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-24:29
action#androidx.media3.session.MediaSessionService
ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-86
	android:name
		ADDED from [:expo-video] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-video\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:25-83
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2f07ca05732640440e3b4e0b986029\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d590517ffbbc1342bd47e61c5be20d2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d590517ffbbc1342bd47e61c5be20d2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.ramsha_malik.tms_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.ramsha_malik.tms_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
