C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-async-storage\async-storage\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\datetimepicker\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-edge-to-edge\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-gesture-handler\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-reanimated\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-safe-area-context\android\src\main\jni\CMakeLists.txt
C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-screens\android\src\main\jni\CMakeLists.txt
C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-svg\android\src\main\jni\CMakeLists.txt
C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\generated\source\codegen\jni\CMakeLists.txt
C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup\CMakeLists.txt