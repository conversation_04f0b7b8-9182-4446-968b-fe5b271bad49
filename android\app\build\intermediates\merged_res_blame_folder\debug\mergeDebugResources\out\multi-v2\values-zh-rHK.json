{"logs": [{"outputFile": "com.ramsha_malik.tms_app-mergeDebugResources-60:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dbf0f811c0b4cd74b2ceefe96db4396e\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "56,57,58,59,60,61,62,228", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3808,3900,3999,4093,4187,4280,4373,15740", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3895,3994,4088,4182,4275,4368,4464,15836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d57699f3eb162dde04aba764d9f38f04\\transformed\\media3-exoplayer-1.4.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7827,7883,7939,7997,8050,8122,8176,8250,8326", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "7878,7934,7992,8045,8117,8171,8245,8321,8380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\428bacad40a6f9fbc888386871dcda9e\\transformed\\material-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1240,1301,1358,1414,1473,1531,1585,1671,1727,1785,1839,1904,1997,2071,2143,2223,2297,2375,2495,2558,2621,2720,2797,2871,2921,2972,3038,3102,3170,3241,3313,3374,3445,3512,3572,3660,3740,3803,3886,3971,4045,4110,4186,4234,4308,4372,4448,4526,4588,4652,4715,4781,4861,4941,5017,5098,5152,5207,5276,5351,5424", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1235,1296,1353,1409,1468,1526,1580,1666,1722,1780,1834,1899,1992,2066,2138,2218,2292,2370,2490,2553,2616,2715,2792,2866,2916,2967,3033,3097,3165,3236,3308,3369,3440,3507,3567,3655,3735,3798,3881,3966,4040,4105,4181,4229,4303,4367,4443,4521,4583,4647,4710,4776,4856,4936,5012,5093,5147,5202,5271,5346,5419,5489"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,68,69,137,139,142,144,145,146,147,148,149,150,151,152,153,154,155,156,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,217,218,219", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "697,3471,3534,3595,3662,3731,4469,4559,4666,4878,4929,9358,9503,9703,9826,9904,9965,10022,10078,10137,10195,10249,10335,10391,10449,10503,10568,11290,11364,11436,11516,11590,11668,11788,11851,11914,12013,12090,12164,12214,12265,12331,12395,12463,12534,12606,12667,12738,12805,12865,12953,13033,13096,13179,13264,13338,13403,13479,13527,13601,13665,13741,13819,13881,13945,14008,14074,14154,14234,14310,14391,14445,14500,14933,15008,15081", "endLines": "22,51,52,53,54,55,63,64,65,68,69,137,139,142,144,145,146,147,148,149,150,151,152,153,154,155,156,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,217,218,219", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "839,3529,3590,3657,3726,3803,4554,4661,4734,4924,4986,9431,9557,9756,9899,9960,10017,10073,10132,10190,10244,10330,10386,10444,10498,10563,10656,11359,11431,11511,11585,11663,11783,11846,11909,12008,12085,12159,12209,12260,12326,12390,12458,12529,12601,12662,12733,12800,12860,12948,13028,13091,13174,13259,13333,13398,13474,13522,13596,13660,13736,13814,13876,13940,14003,14069,14149,14229,14305,14386,14440,14495,14564,15003,15076,15146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd2f07ca05732640440e3b4e0b986029\\transformed\\react-android-0.79.2-debug\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,326,401,466,531,600,671,744,819,886,956,1029,1101,1178,1254,1326,1396,1465,1545,1613,1683,1750", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,188,255,321,396,461,526,595,666,739,814,881,951,1024,1096,1173,1249,1321,1391,1460,1540,1608,1678,1745,1814"}, "to": {"startLines": "50,66,138,140,141,143,163,164,165,212,213,214,215,220,221,222,223,224,225,226,227,229,230,231,232", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3405,4739,9436,9562,9628,9761,11085,11150,11219,14569,14642,14717,14784,15151,15224,15296,15373,15449,15521,15591,15660,15841,15909,15979,16046", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "3466,4806,9498,9623,9698,9821,11145,11214,11285,14637,14712,14779,14849,15219,15291,15368,15444,15516,15586,15655,15735,15904,15974,16041,16110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3f7996bce1dd550dfb6c254808c8db0c\\transformed\\media3-ui-1.4.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,602,672,741,811,887,962,1017,1078,1152,1226,1288,1349,1408,1473,1562,1648,1737,1800,1867,1932,1987,2061,2134,2195,2258,2310,2368,2415,2476,2532,2594,2651,2711,2767,2822,2885,2934,2987,3054,3121,3170", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,48,52,66,66,48,60", "endOffsets": "276,439,597,667,736,806,882,957,1012,1073,1147,1221,1283,1344,1403,1468,1557,1643,1732,1795,1862,1927,1982,2056,2129,2190,2253,2305,2363,2410,2471,2527,2589,2646,2706,2762,2817,2880,2929,2982,3049,3116,3165,3226"}, "to": {"startLines": "2,11,15,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,376,539,6171,6241,6310,6380,6456,6531,6586,6647,6721,6795,6857,6918,6977,7042,7131,7217,7306,7369,7436,7501,7556,7630,7703,7764,8385,8437,8495,8542,8603,8659,8721,8778,8838,8894,8949,9012,9061,9114,9181,9248,9297", "endLines": "10,14,18,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,48,52,66,66,48,60", "endOffsets": "371,534,692,6236,6305,6375,6451,6526,6581,6642,6716,6790,6852,6913,6972,7037,7126,7212,7301,7364,7431,7496,7551,7625,7698,7759,7822,8432,8490,8537,8598,8654,8716,8773,8833,8889,8944,9007,9056,9109,9176,9243,9292,9353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\29b9c3bc2763a8fc1ca54c4fbd792047\\transformed\\media3-session-1.4.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,201,261,340,417,485,553,616,688,754,807,884,951,1028,1093,1169,1233,1302,1367,1431,1500,1572,1647", "endColumns": "66,78,59,78,76,67,67,62,71,65,52,76,66,76,64,75,63,68,64,63,68,71,74,78", "endOffsets": "117,196,256,335,412,480,548,611,683,749,802,879,946,1023,1088,1164,1228,1297,1362,1426,1495,1567,1642,1721"}, "to": {"startLines": "67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4811,4991,5070,5130,5209,5286,5354,5422,5485,5557,5623,5676,5753,5820,5897,5962,6038,6102,10661,10726,10790,10859,10931,11006", "endColumns": "66,78,59,78,76,67,67,62,71,65,52,76,66,76,64,75,63,68,64,63,68,71,74,78", "endOffsets": "4873,5065,5125,5204,5281,5349,5417,5480,5552,5618,5671,5748,5815,5892,5957,6033,6097,6166,10721,10785,10854,10926,11001,11080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bce72c8d6c2b594d45298a87e02b9e4\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "844,939,1032,1132,1214,1311,1419,1496,1571,1663,1757,1848,1944,2039,2133,2229,2321,2413,2505,2583,2679,2774,2869,2966,3062,3160,3311,14854", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "934,1027,1127,1209,1306,1414,1491,1566,1658,1752,1843,1939,2034,2128,2224,2316,2408,2500,2578,2674,2769,2864,2961,3057,3155,3306,3400,14928"}}]}]}