{"logs": [{"outputFile": "com.ramsha_malik.tms_app-mergeDebugResources-60:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d57699f3eb162dde04aba764d9f38f04\\transformed\\media3-exoplayer-1.4.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,194,266,336,413,484,575,660", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "122,189,261,331,408,479,570,655,734"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8882,8954,9021,9093,9163,9240,9311,9402,9487", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "8949,9016,9088,9158,9235,9306,9397,9482,9561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\428bacad40a6f9fbc888386871dcda9e\\transformed\\material-1.12.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1038,1103,1197,1267,1326,1416,1480,1549,1607,1676,1736,1800,1912,1971,2030,2085,2160,2283,2363,2446,2540,2627,2711,2844,2926,3007,3138,3225,3307,3365,3421,3487,3562,3642,3713,3792,3859,3934,4011,4075,4182,4276,4346,4435,4528,4602,4677,4767,4823,4902,4969,5053,5137,5199,5263,5326,5392,5492,5599,5693,5801,5863,5923,6003,6088,6169", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "254,332,410,488,586,675,775,894,977,1033,1098,1192,1262,1321,1411,1475,1544,1602,1671,1731,1795,1907,1966,2025,2080,2155,2278,2358,2441,2535,2622,2706,2839,2921,3002,3133,3220,3302,3360,3416,3482,3557,3637,3708,3787,3854,3929,4006,4070,4177,4271,4341,4430,4523,4597,4672,4762,4818,4897,4964,5048,5132,5194,5258,5321,5387,5487,5594,5688,5796,5858,5918,5998,6083,6164,6238"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,68,69,137,139,142,144,145,146,147,148,149,150,151,152,153,154,155,156,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,217,218,219", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "770,3724,3802,3880,3958,4056,4866,4966,5085,5322,5378,10598,10761,10980,11105,11195,11259,11328,11386,11455,11515,11579,11691,11750,11809,11864,11939,12755,12835,12918,13012,13099,13183,13316,13398,13479,13610,13697,13779,13837,13893,13959,14034,14114,14185,14264,14331,14406,14483,14547,14654,14748,14818,14907,15000,15074,15149,15239,15295,15374,15441,15525,15609,15671,15735,15798,15864,15964,16071,16165,16273,16335,16395,16868,16953,17034", "endLines": "22,51,52,53,54,55,63,64,65,68,69,137,139,142,144,145,146,147,148,149,150,151,152,153,154,155,156,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,217,218,219", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "924,3797,3875,3953,4051,4140,4961,5080,5163,5373,5438,10687,10826,11034,11190,11254,11323,11381,11450,11510,11574,11686,11745,11804,11859,11934,12057,12830,12913,13007,13094,13178,13311,13393,13474,13605,13692,13774,13832,13888,13954,14029,14109,14180,14259,14326,14401,14478,14542,14649,14743,14813,14902,14995,15069,15144,15234,15290,15369,15436,15520,15604,15666,15730,15793,15859,15959,16066,16160,16268,16330,16390,16470,16948,17029,17103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd2f07ca05732640440e3b4e0b986029\\transformed\\react-android-0.79.2-debug\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,269,338,418,484,552,627,704,787,866,934,1011,1093,1167,1250,1336,1412,1485,1557,1646,1717,1793,1862", "endColumns": "67,76,68,68,79,65,67,74,76,82,78,67,76,81,73,82,85,75,72,71,88,70,75,68,72", "endOffsets": "118,195,264,333,413,479,547,622,699,782,861,929,1006,1088,1162,1245,1331,1407,1480,1552,1641,1712,1788,1857,1930"}, "to": {"startLines": "50,66,138,140,141,143,163,164,165,212,213,214,215,220,221,222,223,224,225,226,227,229,230,231,232", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3656,5168,10692,10831,10900,11039,12535,12603,12678,16475,16558,16637,16705,17108,17190,17264,17347,17433,17509,17582,17654,17844,17915,17991,18060", "endColumns": "67,76,68,68,79,65,67,74,76,82,78,67,76,81,73,82,85,75,72,71,88,70,75,68,72", "endOffsets": "3719,5240,10756,10895,10975,11100,12598,12673,12750,16553,16632,16700,16777,17185,17259,17342,17428,17504,17577,17649,17738,17910,17986,18055,18128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\29b9c3bc2763a8fc1ca54c4fbd792047\\transformed\\media3-session-1.4.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,240,307,402,503,588,675,761,854,940,1007,1104,1185,1272,1351,1440,1513,1618,1686,1754,1831,1910,1998", "endColumns": "76,107,66,94,100,84,86,85,92,85,66,96,80,86,78,88,72,104,67,67,76,78,87,92", "endOffsets": "127,235,302,397,498,583,670,756,849,935,1002,1099,1180,1267,1346,1435,1508,1613,1681,1749,1826,1905,1993,2086"}, "to": {"startLines": "67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5245,5443,5551,5618,5713,5814,5899,5986,6072,6165,6251,6318,6415,6496,6583,6662,6751,6824,12062,12130,12198,12275,12354,12442", "endColumns": "76,107,66,94,100,84,86,85,92,85,66,96,80,86,78,88,72,104,67,67,76,78,87,92", "endOffsets": "5317,5546,5613,5708,5809,5894,5981,6067,6160,6246,6313,6410,6491,6578,6657,6746,6819,6924,12125,12193,12270,12349,12437,12530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3f7996bce1dd550dfb6c254808c8db0c\\transformed\\media3-ui-1.4.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,488,675,763,854,939,1034,1129,1197,1259,1348,1437,1507,1572,1634,1702,1812,1924,2033,2107,2188,2258,2326,2412,2501,2565,2628,2681,2739,2787,2848,2908,2977,3037,3100,3160,3223,3288,3341,3398,3469,3540,3594", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,109,111,108,73,80,69,67,85,88,63,62,52,57,47,60,59,68,59,62,59,62,64,52,56,70,70,53,65", "endOffsets": "277,483,670,758,849,934,1029,1124,1192,1254,1343,1432,1502,1567,1629,1697,1807,1919,2028,2102,2183,2253,2321,2407,2496,2560,2623,2676,2734,2782,2843,2903,2972,3032,3095,3155,3218,3283,3336,3393,3464,3535,3589,3655"}, "to": {"startLines": "2,11,15,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,377,583,6929,7017,7108,7193,7288,7383,7451,7513,7602,7691,7761,7826,7888,7956,8066,8178,8287,8361,8442,8512,8580,8666,8755,8819,9566,9619,9677,9725,9786,9846,9915,9975,10038,10098,10161,10226,10279,10336,10407,10478,10532", "endLines": "10,14,18,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,109,111,108,73,80,69,67,85,88,63,62,52,57,47,60,59,68,59,62,59,62,64,52,56,70,70,53,65", "endOffsets": "372,578,765,7012,7103,7188,7283,7378,7446,7508,7597,7686,7756,7821,7883,7951,8061,8173,8282,8356,8437,8507,8575,8661,8750,8814,8877,9614,9672,9720,9781,9841,9910,9970,10033,10093,10156,10221,10274,10331,10402,10473,10527,10593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bce72c8d6c2b594d45298a87e02b9e4\\transformed\\appcompat-1.7.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "929,1043,1149,1258,1344,1448,1568,1645,1720,1812,1906,2001,2095,2196,2290,2386,2480,2572,2664,2749,2857,2963,3065,3176,3277,3393,3558,16782", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "1038,1144,1253,1339,1443,1563,1640,1715,1807,1901,1996,2090,2191,2285,2381,2475,2567,2659,2744,2852,2958,3060,3171,3272,3388,3553,3651,16863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dbf0f811c0b4cd74b2ceefe96db4396e\\transformed\\core-1.13.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "56,57,58,59,60,61,62,228", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4145,4243,4345,4447,4551,4654,4752,17743", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "4238,4340,4442,4546,4649,4747,4861,17839"}}]}]}