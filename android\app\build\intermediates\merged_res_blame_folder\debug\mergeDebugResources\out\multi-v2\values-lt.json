{"logs": [{"outputFile": "com.ramsha_malik.tms_app-mergeDebugResources-60:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3f7996bce1dd550dfb6c254808c8db0c\\transformed\\media3-ui-1.4.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,298,622,933,1017,1100,1178,1275,1372,1446,1510,1606,1702,1773,1838,1901,1974,2082,2192,2300,2372,2448,2521,2595,2684,2772,2841,2908,2961,3019,3074,3135,3201,3270,3335,3403,3467,3525,3598,3657,3720,3797,3874,3930", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,107,109,107,71,75,72,73,88,87,68,66,52,57,54,60,65,68,64,67,63,57,72,58,62,76,76,55,66", "endOffsets": "293,617,928,1012,1095,1173,1270,1367,1441,1505,1601,1697,1768,1833,1896,1969,2077,2187,2295,2367,2443,2516,2590,2679,2767,2836,2903,2956,3014,3069,3130,3196,3265,3330,3398,3462,3520,3593,3652,3715,3792,3869,3925,3992"}, "to": {"startLines": "2,11,17,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,393,717,7450,7534,7617,7695,7792,7889,7963,8027,8123,8219,8290,8355,8418,8491,8599,8709,8817,8889,8965,9038,9112,9201,9289,9358,10127,10180,10238,10293,10354,10420,10489,10554,10622,10686,10744,10817,10876,10939,11016,11093,11149", "endLines": "10,16,22,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,107,109,107,71,75,72,73,88,87,68,66,52,57,54,60,65,68,64,67,63,57,72,58,62,76,76,55,66", "endOffsets": "388,712,1023,7529,7612,7690,7787,7884,7958,8022,8118,8214,8285,8350,8413,8486,8594,8704,8812,8884,8960,9033,9107,9196,9284,9353,9420,10175,10233,10288,10349,10415,10484,10549,10617,10681,10739,10812,10871,10934,11011,11088,11144,11211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bce72c8d6c2b594d45298a87e02b9e4\\transformed\\appcompat-1.7.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1303,1419,1523,1636,1723,1825,1947,2030,2110,2204,2300,2397,2493,2596,2692,2790,2886,2980,3074,3157,3266,3374,3474,3584,3689,3795,3971,17624", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "1414,1518,1631,1718,1820,1942,2025,2105,2199,2295,2392,2488,2591,2687,2785,2881,2975,3069,3152,3261,3369,3469,3579,3684,3790,3966,4067,17703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\29b9c3bc2763a8fc1ca54c4fbd792047\\transformed\\media3-session-1.4.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,248,328,432,525,613,691,777,874,964,1045,1141,1235,1329,1405,1494,1575,1673,1747,1817,1898,1983,2077", "endColumns": "77,114,79,103,92,87,77,85,96,89,80,95,93,93,75,88,80,97,73,69,80,84,93,102", "endOffsets": "128,243,323,427,520,608,686,772,869,959,1040,1136,1230,1324,1400,1489,1570,1668,1742,1812,1893,1978,2072,2175"}, "to": {"startLines": "73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,163,164,165,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5703,5910,6025,6105,6209,6302,6390,6468,6554,6651,6741,6822,6918,7012,7106,7182,7271,7352,12765,12839,12909,12990,13075,13169", "endColumns": "77,114,79,103,92,87,77,85,96,89,80,95,93,93,75,88,80,97,73,69,80,84,93,102", "endOffsets": "5776,6020,6100,6204,6297,6385,6463,6549,6646,6736,6817,6913,7007,7101,7177,7266,7347,7445,12834,12904,12985,13070,13164,13267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dbf0f811c0b4cd74b2ceefe96db4396e\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "62,63,64,65,66,67,68,234", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4569,4667,4777,4876,4979,5090,5200,18599", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "4662,4772,4871,4974,5085,5195,5315,18695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d57699f3eb162dde04aba764d9f38f04\\transformed\\media3-exoplayer-1.4.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,267,335,416,490,587,682", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "131,195,262,330,411,485,582,677,752"}, "to": {"startLines": "117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9425,9506,9570,9637,9705,9786,9860,9957,10052", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "9501,9565,9632,9700,9781,9855,9952,10047,10122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\428bacad40a6f9fbc888386871dcda9e\\transformed\\material-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1160,1226,1325,1403,1468,1578,1641,1713,1772,1846,1907,1961,2085,2146,2208,2262,2340,2474,2562,2639,2732,2813,2897,3038,3117,3201,3344,3441,3518,3574,3628,3694,3769,3848,3919,3999,4075,4153,4226,4303,4410,4497,4578,4668,4760,4832,4913,5005,5060,5142,5208,5293,5380,5442,5506,5569,5641,5752,5868,5969,6078,6138,6196,6278,6364,6440", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1155,1221,1320,1398,1463,1573,1636,1708,1767,1841,1902,1956,2080,2141,2203,2257,2335,2469,2557,2634,2727,2808,2892,3033,3112,3196,3339,3436,3513,3569,3623,3689,3764,3843,3914,3994,4070,4148,4221,4298,4405,4492,4573,4663,4755,4827,4908,5000,5055,5137,5203,5288,5375,5437,5501,5564,5636,5747,5863,5964,6073,6133,6191,6273,6359,6435,6513"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,74,75,143,145,148,150,151,152,153,154,155,156,157,158,159,160,161,162,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,223,224,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1028,4145,4224,4302,4385,4479,5320,5416,5534,5781,5844,11216,11388,11624,11759,11869,11932,12004,12063,12137,12198,12252,12376,12437,12499,12553,12631,13500,13588,13665,13758,13839,13923,14064,14143,14227,14370,14467,14544,14600,14654,14720,14795,14874,14945,15025,15101,15179,15252,15329,15436,15523,15604,15694,15786,15858,15939,16031,16086,16168,16234,16319,16406,16468,16532,16595,16667,16778,16894,16995,17104,17164,17222,17708,17794,17870", "endLines": "28,57,58,59,60,61,69,70,71,74,75,143,145,148,150,151,152,153,154,155,156,157,158,159,160,161,162,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,223,224,225", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "1298,4219,4297,4380,4474,4564,5411,5529,5613,5839,5905,11310,11461,11684,11864,11927,11999,12058,12132,12193,12247,12371,12432,12494,12548,12626,12760,13583,13660,13753,13834,13918,14059,14138,14222,14365,14462,14539,14595,14649,14715,14790,14869,14940,15020,15096,15174,15247,15324,15431,15518,15599,15689,15781,15853,15934,16026,16081,16163,16229,16314,16401,16463,16527,16590,16662,16773,16889,16990,17099,17159,17217,17299,17789,17865,17943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd2f07ca05732640440e3b4e0b986029\\transformed\\react-android-0.79.2-debug\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,213,286,357,444,514,582,660,742,824,905,979,1062,1146,1224,1307,1390,1466,1542,1616,1713,1788,1870,1943", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "123,208,281,352,439,509,577,655,737,819,900,974,1057,1141,1219,1302,1385,1461,1537,1611,1708,1783,1865,1938,2018"}, "to": {"startLines": "56,72,144,146,147,149,169,170,171,218,219,220,221,226,227,228,229,230,231,232,233,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4072,5618,11315,11466,11537,11689,13272,13340,13418,17304,17386,17467,17541,17948,18032,18110,18193,18276,18352,18428,18502,18700,18775,18857,18930", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "4140,5698,11383,11532,11619,11754,13335,13413,13495,17381,17462,17536,17619,18027,18105,18188,18271,18347,18423,18497,18594,18770,18852,18925,19005"}}]}]}