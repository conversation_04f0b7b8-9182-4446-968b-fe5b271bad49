{"logs": [{"outputFile": "com.ramsha_malik.tms_app-mergeDebugResources-60:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\29b9c3bc2763a8fc1ca54c4fbd792047\\transformed\\media3-session-1.4.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,237,319,436,527,614,689,781,877,967,1052,1145,1227,1320,1398,1494,1568,1657,1725,1793,1874,1957,2052", "endColumns": "76,104,81,116,90,86,74,91,95,89,84,92,81,92,77,95,73,88,67,67,80,82,94,96", "endOffsets": "127,232,314,431,522,609,684,776,872,962,1047,1140,1222,1315,1393,1489,1563,1652,1720,1788,1869,1952,2047,2144"}, "to": {"startLines": "66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5178,5385,5490,5572,5689,5780,5867,5942,6034,6130,6220,6305,6398,6480,6573,6651,6747,6821,12253,12321,12389,12470,12553,12648", "endColumns": "76,104,81,116,90,86,74,91,95,89,84,92,81,92,77,95,73,88,67,67,80,82,94,96", "endOffsets": "5250,5485,5567,5684,5775,5862,5937,6029,6125,6215,6300,6393,6475,6568,6646,6742,6816,6905,12316,12384,12465,12548,12643,12740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d57699f3eb162dde04aba764d9f38f04\\transformed\\media3-exoplayer-1.4.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,192,270,343,424,499,587,674", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "122,187,265,338,419,494,582,669,754"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8988,9060,9125,9203,9276,9357,9432,9520,9607", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "9055,9120,9198,9271,9352,9427,9515,9602,9687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3f7996bce1dd550dfb6c254808c8db0c\\transformed\\media3-ui-1.4.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,483,673,757,841,920,1018,1120,1205,1270,1369,1468,1533,1598,1662,1729,1857,1986,2113,2188,2267,2341,2426,2522,2618,2685,2751,2804,2865,2913,2974,3040,3119,3183,3251,3315,3376,3442,3494,3556,3632,3708,3761", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,51,61,75,75,52,64", "endOffsets": "285,478,668,752,836,915,1013,1115,1200,1265,1364,1463,1528,1593,1657,1724,1852,1981,2108,2183,2262,2336,2421,2517,2613,2680,2746,2799,2860,2908,2969,3035,3114,3178,3246,3310,3371,3437,3489,3551,3627,3703,3756,3821"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,385,578,6910,6994,7078,7157,7255,7357,7442,7507,7606,7705,7770,7835,7899,7966,8094,8223,8350,8425,8504,8578,8663,8759,8855,8922,9692,9745,9806,9854,9915,9981,10060,10124,10192,10256,10317,10383,10435,10497,10573,10649,10702", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,51,61,75,75,52,64", "endOffsets": "380,573,763,6989,7073,7152,7250,7352,7437,7502,7601,7700,7765,7830,7894,7961,8089,8218,8345,8420,8499,8573,8658,8754,8850,8917,8983,9740,9801,9849,9910,9976,10055,10119,10187,10251,10312,10378,10430,10492,10568,10644,10697,10762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bce72c8d6c2b594d45298a87e02b9e4\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "941,1049,1152,1263,1349,1454,1567,1650,1729,1820,1913,2008,2102,2202,2295,2390,2485,2576,2667,2748,2861,2967,3065,3178,3283,3387,3545,17080", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "1044,1147,1258,1344,1449,1562,1645,1724,1815,1908,2003,2097,2197,2290,2385,2480,2571,2662,2743,2856,2962,3060,3173,3278,3382,3540,3639,17157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd2f07ca05732640440e3b4e0b986029\\transformed\\react-android-0.79.2-debug\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,278,368,436,504,581,662,746,826,898,986,1073,1152,1233,1313,1390,1468,1542,1626,1700,1780,1851", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "125,197,273,363,431,499,576,657,741,821,893,981,1068,1147,1228,1308,1385,1463,1537,1621,1695,1775,1846,1929"}, "to": {"startLines": "50,137,139,140,142,162,163,164,211,212,213,214,219,220,221,222,223,224,225,226,228,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3644,10864,11005,11081,11234,12745,12813,12890,16756,16840,16920,16992,17408,17495,17574,17655,17735,17812,17890,17964,18149,18223,18303,18374", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "3714,10931,11076,11166,11297,12808,12885,12966,16835,16915,16987,17075,17490,17569,17650,17730,17807,17885,17959,18043,18218,18298,18369,18452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\428bacad40a6f9fbc888386871dcda9e\\transformed\\material-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1071,1138,1235,1304,1367,1454,1518,1584,1644,1713,1774,1828,1943,2002,2062,2116,2188,2318,2406,2485,2583,2671,2755,2893,2971,3047,3186,3280,3360,3416,3470,3536,3609,3687,3758,3842,3915,3993,4066,4141,4251,4341,4416,4510,4608,4682,4759,4859,4912,4996,5064,5153,5242,5304,5369,5432,5502,5609,5709,5809,5905,5965,6023,6103,6193,6268", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "268,346,420,504,597,691,790,915,1003,1066,1133,1230,1299,1362,1449,1513,1579,1639,1708,1769,1823,1938,1997,2057,2111,2183,2313,2401,2480,2578,2666,2750,2888,2966,3042,3181,3275,3355,3411,3465,3531,3604,3682,3753,3837,3910,3988,4061,4136,4246,4336,4411,4505,4603,4677,4754,4854,4907,4991,5059,5148,5237,5299,5364,5427,5497,5604,5704,5804,5900,5960,6018,6098,6188,6263,6344"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,67,68,136,138,141,143,144,145,146,147,148,149,150,151,152,153,154,155,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "768,3719,3797,3871,3955,4048,4866,4965,5090,5255,5318,10767,10936,11171,11302,11389,11453,11519,11579,11648,11709,11763,11878,11937,11997,12051,12123,12971,13059,13138,13236,13324,13408,13546,13624,13700,13839,13933,14013,14069,14123,14189,14262,14340,14411,14495,14568,14646,14719,14794,14904,14994,15069,15163,15261,15335,15412,15512,15565,15649,15717,15806,15895,15957,16022,16085,16155,16262,16362,16462,16558,16618,16676,17162,17252,17327", "endLines": "22,51,52,53,54,55,63,64,65,67,68,136,138,141,143,144,145,146,147,148,149,150,151,152,153,154,155,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "936,3792,3866,3950,4043,4137,4960,5085,5173,5313,5380,10859,11000,11229,11384,11448,11514,11574,11643,11704,11758,11873,11932,11992,12046,12118,12248,13054,13133,13231,13319,13403,13541,13619,13695,13834,13928,14008,14064,14118,14184,14257,14335,14406,14490,14563,14641,14714,14789,14899,14989,15064,15158,15256,15330,15407,15507,15560,15644,15712,15801,15890,15952,16017,16080,16150,16257,16357,16457,16553,16613,16671,16751,17247,17322,17403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dbf0f811c0b4cd74b2ceefe96db4396e\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "56,57,58,59,60,61,62,227", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4142,4238,4340,4439,4538,4644,4748,18048", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "4233,4335,4434,4533,4639,4743,4861,18144"}}]}]}